"""
Custom Spinner Component

Provides a modern custom ball animation spinner with elapsed time display.
"""

import time
import threading
from typing import Optional
from rich.console import Console
from rich.text import Text
from rich.live import Live
from .base_component import BaseUIComponent


class SpinnerComponent(BaseUIComponent):
    """
    Custom ball animation spinner with elapsed time.
    
    Features:
    - Modern ball animation frames
    - Elapsed time display
    - Customizable text and style
    - Thread-safe operation
    """
    
    def __init__(self, console: Optional[Console] = None):
        super().__init__("spinner", console)
        
        # Ball animation frames
        self.ball_frames = [
            "( ●    )",
            "(  ●   )",
            "(   ●  )",
            "(    ● )",
            "(     ●)",
            "(    ● )",
            "(   ●  )",
            "(  ●   )",
            "( ●    )",
            "(●     )",
        ]
        
        self._current_frame = 0
        self._start_time: Optional[float] = None
        self._stop_event = threading.Event()
        self._live: Optional[Live] = None
        
    def render(self, text: str = "Processing", style: str = "bold green") -> "SpinnerComponent":
        """
        Start the spinner with given text.
        
        Args:
            text: Text to display next to spinner
            style: Rich style for the text
            
        Returns:
            Self for method chaining
        """
        self.set_state("text", text)
        self.set_state("style", style)
        self._start_time = time.time()
        self._stop_event.clear()
        
        # Create live display
        self._live = Live(
            self._get_spinner_text(),
            console=self.console,
            refresh_per_second=10,
            transient=True
        )
        
        self.log_debug(f"Starting spinner with text: {text}")
        return self
    
    def _get_spinner_text(self) -> Text:
        """Get the current spinner text with animation and elapsed time."""
        if self._start_time is None:
            elapsed = 0
        else:
            elapsed = time.time() - self._start_time
        
        # Get current frame
        frame = self.ball_frames[self._current_frame % len(self.ball_frames)]
        self._current_frame += 1
        
        # Format elapsed time
        if elapsed < 60:
            time_str = f"{elapsed:.1f}s"
        else:
            minutes = int(elapsed // 60)
            seconds = elapsed % 60
            time_str = f"{minutes}m {seconds:.1f}s"
        
        # Create text with spinner, message, and elapsed time
        text = Text()
        text.append(frame, style="cyan")
        text.append(" ")
        text.append(self.get_state("text", "Processing"), style=self.get_state("style", "bold green"))
        text.append(" ")
        text.append(f"({time_str})", style="dim")
        
        return text
    
    def update_text(self, text: str) -> None:
        """Update the spinner text without restarting."""
        self.set_state("text", text)
        self.log_debug(f"Updated spinner text: {text}")
    
    def start(self) -> None:
        """Start the live display."""
        if self._live:
            self._live.start()
            self.log_debug("Spinner started")
    
    def stop(self) -> None:
        """Stop the spinner."""
        self._stop_event.set()
        if self._live:
            self._live.stop()
            self._live = None
        self.log_debug("Spinner stopped")
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()


def create_spinner(text: str = "Processing", style: str = "bold green", console: Optional[Console] = None) -> SpinnerComponent:
    """
    Create and configure a spinner component.
    
    Args:
        text: Text to display next to spinner
        style: Rich style for the text
        console: Rich console instance
        
    Returns:
        Configured SpinnerComponent
    """
    spinner = SpinnerComponent(console)
    return spinner.render(text, style)
