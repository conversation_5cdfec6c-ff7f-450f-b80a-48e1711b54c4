"""
Login Component

Handles LLM provider configuration and system activation.
"""

import os
from typing import Optional, Dict, Any, List
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.table import Table
from .base_component import BaseUIComponent


class LoginComponent(BaseUIComponent):
    """
    Login component for configuring LLM providers and activating the system.
    
    Features:
    - Provider selection (DeepSeek, Ollama)
    - API key configuration
    - Model selection
    - Configuration validation
    - Auto-login support
    """
    
    def __init__(self, console: Optional[Console] = None):
        super().__init__("login", console)
        
        # Supported providers and their default models
        self.providers = {
            "deepseek": {
                "name": "DeepSeek",
                "models": ["deepseek-chat", "deepseek-reasoner"],
                "requires_api_key": True,
                "api_key_env": "DEEPSEEK_API_KEY"
            },
            "ollama": {
                "name": "Ollama",
                "models": ["llama3.2", "llama3.1", "codellama", "mistral"],
                "requires_api_key": False,
                "host_env": "OLLAMA_HOST"
            }
        }
    
    def render(self, auto_login: bool = False) -> Dict[str, Any]:
        """
        Render the login interface and configure LLM provider.
        
        Args:
            auto_login: Skip interactive login if configuration exists
            
        Returns:
            Configuration dictionary with provider, model, and credentials
        """
        self.log_info("Starting login process")
        
        # Check for existing configuration
        if auto_login and self._has_valid_config():
            config = self._load_existing_config()
            self.log_info(f"Auto-login successful with {config['provider']}")
            return config
        
        # Display login banner
        self._display_login_banner()
        
        # Interactive configuration
        config = self._interactive_configuration()
        
        # Validate configuration
        if self._validate_configuration(config):
            self._save_configuration(config)
            self._display_success(config)
            self.log_info(f"Login successful with {config['provider']}")
            return config
        else:
            self.console.print("[bold red]Configuration validation failed![/bold red]")
            return self.render(auto_login=False)  # Retry
    
    def _display_login_banner(self) -> None:
        """Display the login banner."""
        banner_text = Text()
        banner_text.append("🔐 ", style="bold yellow")
        banner_text.append("LLM Provider Configuration", style="bold cyan")
        
        panel = Panel(
            banner_text,
            border_style="yellow",
            padding=(1, 2),
            title="[bold yellow]System Activation[/bold yellow]",
            title_align="center",
        )
        self.console.print(panel)
        self.console.print()
    
    def _interactive_configuration(self) -> Dict[str, Any]:
        """Interactive provider configuration."""
        config = {}
        
        # Provider selection
        config["provider"] = self._select_provider()
        provider_info = self.providers[config["provider"]]
        
        # API key configuration (if required)
        if provider_info["requires_api_key"]:
            config["api_key"] = self._configure_api_key(config["provider"])
        
        # Model selection
        config["model"] = self._select_model(config["provider"])
        
        # Additional configuration
        if config["provider"] == "ollama":
            config["host"] = self._configure_ollama_host()
        
        return config
    
    def _select_provider(self) -> str:
        """Select LLM provider."""
        self.console.print("[bold cyan]Available LLM Providers:[/bold cyan]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Option", style="cyan")
        table.add_column("Provider", style="white")
        table.add_column("Description", style="dim")
        
        for i, (key, info) in enumerate(self.providers.items(), 1):
            description = "API Key Required" if info["requires_api_key"] else "Local/Self-hosted"
            table.add_row(str(i), info["name"], description)
        
        self.console.print(table)
        self.console.print()
        
        while True:
            choice = Prompt.ask(
                "[bold blue]Select provider",
                choices=[str(i) for i in range(1, len(self.providers) + 1)],
                default="1"
            )
            
            provider_keys = list(self.providers.keys())
            return provider_keys[int(choice) - 1]
    
    def _configure_api_key(self, provider: str) -> str:
        """Configure API key for provider."""
        provider_info = self.providers[provider]
        env_var = provider_info["api_key_env"]
        
        # Check if already set in environment
        existing_key = os.getenv(env_var)
        if existing_key:
            if Confirm.ask(f"[yellow]Use existing {provider_info['name']} API key from environment?[/yellow]"):
                return existing_key
        
        self.console.print(f"[bold yellow]Configure {provider_info['name']} API Key[/bold yellow]")
        self.console.print(f"[dim]This will be stored in environment variable: {env_var}[/dim]")
        
        while True:
            api_key = Prompt.ask(
                f"[bold blue]Enter {provider_info['name']} API key",
                password=True
            )
            
            if api_key.strip():
                # Set environment variable
                os.environ[env_var] = api_key
                return api_key
            else:
                self.console.print("[bold red]API key cannot be empty![/bold red]")
    
    def _select_model(self, provider: str) -> str:
        """Select model for provider."""
        provider_info = self.providers[provider]
        models = provider_info["models"]
        
        self.console.print(f"[bold cyan]Available {provider_info['name']} Models:[/bold cyan]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Option", style="cyan")
        table.add_column("Model", style="white")
        
        for i, model in enumerate(models, 1):
            table.add_row(str(i), model)
        
        self.console.print(table)
        self.console.print()
        
        choice = Prompt.ask(
            "[bold blue]Select model",
            choices=[str(i) for i in range(1, len(models) + 1)],
            default="1"
        )
        
        return models[int(choice) - 1]
    
    def _configure_ollama_host(self) -> str:
        """Configure Ollama host."""
        default_host = "http://localhost:11434"
        existing_host = os.getenv("OLLAMA_HOST", default_host)
        
        host = Prompt.ask(
            "[bold blue]Ollama host URL",
            default=existing_host
        )
        
        os.environ["OLLAMA_HOST"] = host
        return host
    
    def _has_valid_config(self) -> bool:
        """Check if valid configuration exists."""
        # Check for DeepSeek
        if os.getenv("DEEPSEEK_API_KEY"):
            return True
        
        # Check for Ollama (always available if host is reachable)
        return True  # Simplified for now
    
    def _load_existing_config(self) -> Dict[str, Any]:
        """Load existing configuration."""
        if os.getenv("DEEPSEEK_API_KEY"):
            return {
                "provider": "deepseek",
                "model": "deepseek-chat",
                "api_key": os.getenv("DEEPSEEK_API_KEY")
            }
        else:
            return {
                "provider": "ollama",
                "model": "llama3.2",
                "host": os.getenv("OLLAMA_HOST", "http://localhost:11434")
            }
    
    def _validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate the configuration."""
        # Basic validation
        if not config.get("provider") or not config.get("model"):
            return False
        
        provider_info = self.providers[config["provider"]]
        
        # Check API key if required
        if provider_info["requires_api_key"] and not config.get("api_key"):
            return False
        
        return True
    
    def _save_configuration(self, config: Dict[str, Any]) -> None:
        """Save configuration to settings."""
        self.log_debug(f"Saving configuration: {config['provider']}/{config['model']}")
        # Configuration is already saved to environment variables
        pass
    
    def _display_success(self, config: Dict[str, Any]) -> None:
        """Display successful configuration."""
        success_text = Text()
        success_text.append("✅ ", style="bold green")
        success_text.append("Configuration Complete!", style="bold green")
        
        details = Text()
        details.append(f"Provider: {self.providers[config['provider']]['name']}\n", style="cyan")
        details.append(f"Model: {config['model']}\n", style="white")
        
        panel = Panel(
            details,
            title=success_text,
            border_style="green",
            padding=(1, 2),
        )
        self.console.print(panel)
        self.console.print()
